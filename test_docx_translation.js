// Test script to verify DOCX translation logic with text nodes

// Mock data structure similar to what we get from DOCX parsing
const testParagraph = {
  element: [
    {
      'w:pPr': [
        {
          'w:pStyle': [],
          ':@': {
            '@_w:val': 'NoSpacing'
          }
        }
      ]
    },
    {
      'w:r': [
        {
          'w:rPr': []
        },
        {
          'w:t': [
            {
              '#text':
                '【日本語】 ベトナムに輸入される商品の原ラベルには、通関手続中に以下の情報がベトナム語または外国語で記載されていなければならない：'
            }
          ]
        }
      ]
    },
    {
      'w:r': [
        {
          'w:rPr': []
        },
        {
          'w:br': []
        },
        {
          'w:t': [
            {
              '#text': 'a) '
            }
          ]
        }
      ]
    },
    {
      'w:r': [
        {
          'w:rPr': []
        },
        {
          'w:t': [
            {
              '#text': '商品の名称'
            }
          ]
        }
      ]
    },
    {
      'w:r': [
        {
          'w:rPr': []
        },
        {
          'w:br': []
        },
        {
          'w:t': [
            {
              '#text': 'b) '
            }
          ]
        }
      ]
    },
    {
      'w:r': [
        {
          'w:rPr': []
        },
        {
          'w:t': [
            {
              '#text': '商品の原産地。原産地が不明な場合は、商品の最終仕上げ工程が行われた国'
            }
          ]
        }
      ]
    }
  ],
  originalText:
    '【日本語】 ベトナムに輸入される商品の原ラベルには、通関手続中に以下の情報がベトナム語または外国語で記載されていなければならない：a) 商品の名称b) 商品の原産地。原産地が不明な場合は、商品の最終仕上げ工程が行われた国',
  textNodes: [
    {
      '#text':
        '【日本語】 ベトナムに輸入される商品の原ラベルには、通関手続中に以下の情報がベトナム語または外国語で記載されていなければならない：'
    },
    {
      '#text': 'a) '
    },
    {
      '#text': '商品の名称'
    },
    {
      '#text': 'b) '
    },
    {
      '#text': '商品の原産地。原産地が不明な場合は、商品の最終仕上げ工程が行われた国'
    }
  ]
}

// Test the new logic
function testTextNodeTranslation() {
  console.log('=== Testing Text Node Translation Logic ===')

  // Step 1: Create text for API (like the new logic)
  const textNodeTexts = testParagraph.textNodes.map((node) => node['#text'] || '').filter((text) => text.trim())
  const paragraphText = textNodeTexts.join('__LINEBREAK__')

  console.log('Original text nodes:')
  textNodeTexts.forEach((text, i) => console.log(`  ${i}: "${text}"`))

  console.log('\nText sent to API:')
  console.log(`"${paragraphText}"`)

  // Step 2: Simulate API response (translated text with __LINEBREAK__)
  const simulatedTranslation =
    '[Vietnamese] Goods imported into Vietnam must have the following information on the original label in Vietnamese or foreign language during customs procedures:__LINEBREAK__a) __LINEBREAK__Product name__LINEBREAK__b) __LINEBREAK__Country of origin of the product. If the origin is unknown, the country where the final finishing process of the product was carried out'

  console.log('\nSimulated API response:')
  console.log(`"${simulatedTranslation}"`)

  // Step 3: Apply the new translation logic
  const translatedTextParts = simulatedTranslation.split('__LINEBREAK__')

  console.log('\nTranslated text parts:')
  translatedTextParts.forEach((part, i) => console.log(`  ${i}: "${part}"`))

  // Step 4: Apply to text nodes
  const testTextNodes = JSON.parse(JSON.stringify(testParagraph.textNodes)) // Deep copy

  if (testTextNodes.length === 1) {
    testTextNodes[0]['#text'] = simulatedTranslation.replace(/__LINEBREAK__/g, '')
  } else {
    for (let i = 0; i < testTextNodes.length; i++) {
      if (i < translatedTextParts.length) {
        testTextNodes[i]['#text'] = translatedTextParts[i]
      } else {
        testTextNodes[i]['#text'] = ''
      }
    }
  }

  console.log('\nFinal text nodes after translation:')
  testTextNodes.forEach((node, i) => console.log(`  ${i}: "${node['#text']}"`))

  // Step 5: Verify the result
  const finalText = testTextNodes.map((node) => node['#text']).join('')
  const expectedText = simulatedTranslation.replace(/__LINEBREAK__/g, '')

  console.log('\nVerification:')
  console.log(`Final combined text: "${finalText}"`)
  console.log(`Expected text: "${expectedText}"`)
  console.log(`Match: ${finalText === expectedText}`)

  return finalText === expectedText
}

// Test case for single text node
function testSingleTextNode() {
  console.log('\n=== Testing Single Text Node Logic ===')

  const singleNodeParagraph = {
    textNodes: [
      {
        '#text': 'This is a single text node with some content.'
      }
    ]
  }

  const simulatedTranslation = 'Đây là một text node duy nhất với một số nội dung.'

  console.log('Original single text node:')
  console.log(`"${singleNodeParagraph.textNodes[0]['#text']}"`)

  console.log('\nSimulated API response:')
  console.log(`"${simulatedTranslation}"`)

  // Apply single text node logic
  const testTextNodes = JSON.parse(JSON.stringify(singleNodeParagraph.textNodes))

  if (testTextNodes.length === 1) {
    testTextNodes[0]['#text'] = simulatedTranslation.replace(/__LINEBREAK__/g, '')
  }

  console.log('\nFinal text node after translation:')
  console.log(`"${testTextNodes[0]['#text']}"`)

  const success = testTextNodes[0]['#text'] === simulatedTranslation
  console.log(`Match: ${success}`)

  return success
}

// Run the tests
const testResult1 = testTextNodeTranslation()
console.log(`\n=== Multiple Text Nodes Test Result: ${testResult1 ? 'PASSED' : 'FAILED'} ===`)

const testResult2 = testSingleTextNode()
console.log(`\n=== Single Text Node Test Result: ${testResult2 ? 'PASSED' : 'FAILED'} ===`)

console.log(`\n=== Overall Test Result: ${testResult1 && testResult2 ? 'ALL PASSED' : 'SOME FAILED'} ===`)
